'use client';

import { useContext, useEffect, useState, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { AuthContext } from '@/components/context/AuthContext';
import Loader1 from '@/components/loaders/Loader1';

interface ProtectedRouteProps {
  children: React.ReactNode;
  roles?: string[]; // Optional roles for role-based access control
  requiresAdmin?: boolean; // Flag to indicate if route requires admin privileges
  requiresStaff?: boolean; // Flag to indicate if route requires staff privileges
}

/**
 * Component to protect routes that require authentication
 * Redirects to login if user is not authenticated
 * Redirects to unauthorized page if user lacks necessary permissions
 */
const ProtectedRoute = ({ 
  children, 
  roles, 
  requiresAdmin = false, 
  requiresStaff = false 
}: ProtectedRouteProps) => {
  const { isAuthenticated, isLoading } = useContext(AuthContext);
  const router = useRouter();
  const pathname = usePathname();
  const [isChecking, setIsChecking] = useState(true);
  const [hasPermission, setHasPermission] = useState(true);
  const authCheckInProgressRef = useRef(false);

  useEffect(() => {
    // Prevent duplicate auth checks
    if (authCheckInProgressRef.current) {
      return;
    }

    const verifyAuth = async () => {
      authCheckInProgressRef.current = true;

      try {
        // Wait for authentication to complete before making any decisions
        if (isLoading) {
          setIsChecking(true);
          return;
        }

        // If authentication is complete and user is authenticated, check permissions
        if (isAuthenticated) {
          // Here you would check user roles/permissions
          // This is a placeholder for the permission check
          // In a real application, you'd check against user roles stored in context or fetched from API
          const userHasPermission = true; // Replace with actual permission check

          if (requiresAdmin || requiresStaff || roles?.length) {
            // If permissions are required but user doesn't have them
            if (!userHasPermission) {
              setHasPermission(false);
              router.push('/unauthorized');
              return;
            }
          }

          setHasPermission(true);
          setIsChecking(false);
          return;
        }

        // If authentication is complete but user is not authenticated,
        // let AuthContext handle the redirect - don't duplicate the logic here
        if (!isAuthenticated && !isLoading) {
          // Just wait for AuthContext to handle the redirect
          setIsChecking(false);
          return;
        }

      } catch (error) {
        console.error('Authentication verification failed:', error);
        // Don't redirect here - let AuthContext handle authentication redirects
        setIsChecking(false);
      } finally {
        authCheckInProgressRef.current = false;
      }
    };

    verifyAuth();
  }, [pathname, isAuthenticated, isLoading, requiresAdmin, requiresStaff, roles, router]); // Removed checkAuth dependency to prevent loops
  // Show loading state while checking authentication
  if (isLoading || isChecking) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <Loader1 />
      </div>
    );
  }

  // If authenticated and has permission, render children
  return (isAuthenticated && hasPermission) ? <>{children}</> : null;
};

export default ProtectedRoute;
