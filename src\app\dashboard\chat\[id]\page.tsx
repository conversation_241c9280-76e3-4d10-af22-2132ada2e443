'use client';

import React, { useContext, useEffect, useState } from 'react';
import { use } from 'react';
import { useRouter } from 'next/navigation';
import { AuthContext } from '@/components/context/AuthContext';
import { useChatData } from '@/components/context/ChatDataContext';
import { AdminRoute } from '@/components/auth/AdminRoute';
import ChatDetailView from '@/components/dashboard/ChatDetailView';
import ChatDashboardHeader from '@/components/dashboard/ChatDashboardHeader';
import { SupportChat } from '@/components/dashboard/SupportChatList';
import { Button } from '@/components/ui/button';
import Sidebar from '@/components/dashboard/Sidebar';

// Get a specific chat by ID using the API client
import { fetchChatById } from '@/services/api';

const getChatData = async (id: string): Promise<SupportChat | null> => {
  try {
    const chatData = await fetchChatById(id);
    return chatData;
  } catch (error) {
    console.error('Errore nel recupero dei dettagli della chat:', error);
    return null;
  }
};

export default function ChatDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const { setIsSidepanelOpen } = useContext(AuthContext);
  const { updateStatus } = useChatData();
  const [chat, setChat] = useState<SupportChat | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Properly unwrap params using React.use()
  const resolvedParams = use(params);
  const chatId = resolvedParams.id;

  useEffect(() => {
    if (!chatId) return;
    
    const fetchChat = async () => {
      try {
        setLoading(true);
        const chatData = await getChatData(chatId);
        
        if (chatData) {
          setChat(chatData);
          setError(null);
        } else {
          setError('Failed to load chat details');
        }
      } catch (err) {
        console.error('Error fetching chat:', err);
        setError('Si è verificato un errore durante il caricamento della chat');
      } finally {
        setLoading(false);
      }
    };

    fetchChat();
  }, [chatId]);

  const handleBack = () => {
    router.push('/dashboard');
  };
    const handleStatusChange = async (newStatus: 'pending' | 'in_progress' | 'resolved') => {
    if (chat && chat.id) {
      try {
        // Optimistic update
        setChat({
          ...chat,
          status: newStatus
        });
        
        // Update via ChatDataContext which handles caching and deduplication
        const success = await updateStatus(chat.id.toString(), newStatus);
        
        if (success) {
          // Broadcast the status change to update any listings
          window.dispatchEvent(new CustomEvent('chat-status-updated', {
            detail: { chatId: chat.id, status: newStatus }
          }));
        } else {
          throw new Error('Failed to update status');
        }
        
      } catch (error) {
        console.error('Error updating status:', error);
        // Revert on error
        if (chat) {
          const refreshedChat = await getChatData(chat.id.toString());
          if (refreshedChat) {
            setChat(refreshedChat);
          }
        }
      }
    }
  };
  
  // Ensure the sidebar is closed by default when visiting the chat page
  useEffect(() => {
    // Force sidebar closed on initial load
    const timer = setTimeout(() => {
      setIsSidepanelOpen(false);
    }, 100);
    
    return () => clearTimeout(timer);
  }, [setIsSidepanelOpen]);
    return (
    <AdminRoute>
      <div className="min-h-screen bg-white dark:bg-gray-900 flex flex-col">
        <ChatDashboardHeader />
        
        {/* Add Sidebar component here */}
        <Sidebar />
        
        <div className="flex flex-1 overflow-hidden">
          <main className="flex-1 overflow-hidden flex flex-col relative mt-16">
            {loading ? (
              <div className="flex-1 flex items-center justify-center">
                <div className="flex flex-col items-center gap-4">
                  <div className="w-12 h-12 border-4 border-t-blue-600 border-blue-200 rounded-full animate-spin"></div>
                  <p className="text-muted-foreground">Caricamento dettagli chat...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex-1 flex items-center justify-center p-6">
                <div className="max-w-md w-full bg-red-50 dark:bg-red-900/20 p-6 rounded-lg border border-red-200 dark:border-red-900/30 text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mx-auto mb-4 text-red-500"><circle cx="12" cy="12" r="10"></circle><line x1="12" x2="12" y1="8" y2="12"></line><line x1="12" x2="12.01" y1="16" y2="16"></line></svg>
                  <h3 className="text-xl font-semibold text-red-700 dark:text-red-400 mb-2">Errore nel caricamento della chat</h3>
                  <p className="text-red-600 dark:text-red-300 mb-4">{error}</p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Button 
                      variant="outline" 
                      onClick={() => {
                        if (chatId) {
                          setLoading(true);
                         getChatData(chatId).then(data => {
                            if (data) {
                              setChat(data);
                              setError(null);
                            } else {
                              setError('Failed to load chat details');
                            }
                            setLoading(false);
                          });
                        }
                      }}
                      className="border-red-200 dark:border-red-900/30 text-red-700 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/50"
                    >
                     Riprova
                    </Button>
                    <Button onClick={handleBack}>
                      Torna al Pannello
                    </Button>
                  </div>
                </div>
              </div>
            ) : chat ? (
              <ChatDetailView 
                chat={chat}
                onBack={handleBack}
                onUpdateStatus={handleStatusChange}
              />
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="bg-yellow-50 dark:bg-yellow-900/20 p-6 rounded-lg border border-yellow-200 dark:border-yellow-900/30 text-center max-w-md">
                  <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mx-auto mb-4 text-yellow-500"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" x2="12" y1="9" y2="13"></line><line x1="12" x2="12.01" y1="17" y2="17"></line></svg>
                  <h3 className="text-xl font-semibold text-yellow-700 dark:text-yellow-400 mb-2">Nessuna chat trovata</h3>
                  <p className="text-yellow-600 dark:text-yellow-300 mb-4">Non siamo riusciti a trovare la chat che stai cercando.</p>
                  <Button onClick={handleBack}>
                   Torna al Pannello
                  </Button>
                </div>
              </div>            )}
          </main>
        </div>
      </div>
    </AdminRoute>
  );
}
