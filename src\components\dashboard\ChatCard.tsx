'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { formatDistanceToNow } from 'date-fns';
import { it } from 'date-fns/locale'; // Import Italian locale properly
import { MessageSquare, AlertCircle, Clock, CheckCircle2 } from 'lucide-react';
import { SupportChat } from './SupportChatList';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

// Enhanced interface with proper typing
interface ChatCardProps {
  chat: SupportChat;
  onClick?: () => void;
  isSelected?: boolean;
}

// Type for priority levels
type PriorityLevel = 'high' | 'medium' | 'low';

// Type for chat status
type ChatStatus = 'pending' | 'in_progress' | 'resolved';

const ChatCard = ({ chat, onClick, isSelected = false }: ChatCardProps) => {
  const router = useRouter();

  const handleCardClick = () => {
    if (onClick) {
      onClick();
    } else {
      // Navigate to individual chat page
      router.push(`/dashboard/chat/${chat.id}`);
    }
  };
  const getPriorityIcon = (priority: PriorityLevel): JSX.Element | null => {
    switch (priority) {
      case 'high': return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'medium': return <AlertCircle className="h-4 w-4 text-[#febd49]" />;
      case 'low': return <AlertCircle className="h-4 w-4 text-green-500" />;
      default: return null;
    }
  };

  const getStatusBadge = (status: ChatStatus): JSX.Element => {
    switch (status) {
      case 'pending': 
        return <Badge variant="outline" className="bg-[#febd49]/10 text-[#113158] border-[#febd49]/30 px-3 py-1 font-medium text-xs">
          <Clock className="mr-1.5 h-3 w-3" /> In attesa
        </Badge>;
      case 'in_progress': 
        return <Badge variant="outline" className="bg-[#113158]/10 text-[#113158] border-[#113158]/30 px-3 py-1 font-medium text-xs">
          <Clock className="mr-1.5 h-3 w-3 animate-spin" /> In Corso
        </Badge>;
      case 'resolved': 
        return <Badge variant="outline" className="bg-[#e0e0e0] text-[#113158] border-[#113158]/30 px-3 py-1 font-medium text-xs">
          <CheckCircle2 className="mr-1.5 h-3 w-3" /> Risolto
        </Badge>;
      default: return <Badge variant="outline" className="px-3 py-1 text-xs">{status}</Badge>;
    }
  };

  const getPriorityText = (priority: PriorityLevel): string => {
    // It's better to have a mapping for translations
    const priorityMap: { [key: string]: string } = {
      high: 'Alta',
      medium: 'Media',
      low: 'Bassa',
    };
    return priorityMap[priority] || priority.charAt(0).toUpperCase() + priority.slice(1);
  };

  const formatDate = (dateString: string): string => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: it });
    } catch {
      return 'Data non valida';
    }
  };
    return (    <Card 
      className={`hover:shadow-lg transition-all duration-300 cursor-pointer p-0 h-full
        ${isSelected ? 'ring-2 ring-[#113158] border-[#113158] shadow-lg' : ''} 
        border-[#e0e0e0] hover:border-[#113158]/50 bg-white shadow-sm
        ${chat.priority === 'high' ? 'border-l-4 border-l-red-500' : 
          chat.priority === 'medium' ? 'border-l-4 border-l-[#febd49]' : 
          'border-l-4 border-l-green-500'}
      `}
      onClick={handleCardClick}
    >
      <CardHeader className="pb-3 px-6 pt-5">
        <div className="flex justify-between w-full">
          <div className="flex items-center gap-4">
            <Avatar className="h-12 w-12 border-2 border-[#febd49]/20 shadow-md">
              <AvatarFallback className={`text-white text-lg ${
                chat.priority === 'high' ? 'bg-red-500' : 
                chat.priority === 'medium' ? 'bg-[#febd49]' : 'bg-[#113158]'
              }`}>
                {chat.user_name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-base font-semibold text-[#113158] mb-1">{chat.user_name}</CardTitle>
              <CardDescription className="truncate max-w-[180px] sm:max-w-[220px] md:max-w-[280px] text-xs">
                {chat.user_email}
              </CardDescription>
            </div>
          </div>
          <div className="flex flex-col items-end">
            {getStatusBadge(chat.status as ChatStatus)}
            <span className="text-xs mt-2 text-muted-foreground">
              {formatDate(chat.updated_at)}
            </span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="px-6 pt-3 pb-5">
        <p className="text-sm line-clamp-2 min-h-[48px] text-gray-600">
          {chat.message_snippet}
        </p>
      </CardContent>
      
      <CardFooter className="px-6 py-4 border-t border-[#e0e0e0] flex items-center justify-between bg-[#f9f9f9]">        <div className="flex items-center text-[#113158] hover:text-[#113158]/80 transition-colors">
          <button className="flex items-center px-4 py-2 rounded-full bg-[#113158]/10 hover:bg-[#113158]/15 transition-colors">
            <MessageSquare size={15} className="mr-2" />
            <span className="text-xs font-medium">Rispondi</span>
          </button>
        </div>
        <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-[#f9f9f9]">
          {getPriorityIcon(chat.priority as PriorityLevel)}
          <span className="text-xs font-medium capitalize">
            {getPriorityText(chat.priority as PriorityLevel)}
          </span>
        </div>
      </CardFooter>
    </Card>
  );
};

export default ChatCard;
